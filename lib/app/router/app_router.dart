import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../features/auth/pages/login_page.dart';
import '../../features/auth/pages/register_step_1_page.dart';
import '../../features/auth/pages/register_step_2_page.dart';
import '../../features/auth/providers/auth_provider.dart';
import '../../features/chat/pages/ai_chat_page.dart';
import '../../features/layout/pages/bottom_page.dart';
import '../../features/home/<USER>/home_page.dart';
import '../../features/profile/pages/profile_page.dart';
import '../../features/debug/pages/font_test_page.dart';
import '../../features/forecast/pages/forecast_tab_view.dart';
import '../../features/forecast/pages/forecast_onboarding_page.dart';
import '../../features/home/<USER>/transaction_detail_page.dart';
import '../../features/home/<USER>/manual_entry_page.dart';
import '../../features/profile/pages/language_settings_page.dart';
import '../../features/report/pages/report_page.dart';
import '../../features/shared_space/pages/shared_space_list_page.dart';
import '../../features/shared_space/pages/shared_space_detail_page.dart';
import '../../features/shared_space/pages/invite_success_page.dart';
import '../../features/shared_space/pages/notification_list_page.dart';
import '../../features/shared_space/models/shared_space_models.dart';
import '../../debug/auth_debug_page.dart';

// 1. 创建一个全局的 navigatorKey
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

final appRouterProvider = Provider<GoRouter>((ref) {
  final authState = ref.watch(authProvider);

  return GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: '/home',
    redirect: (BuildContext context, GoRouterState state) {
      final authStatus = authState.status;
      final String location = state.matchedLocation;

      // 定义不需要登录即可访问的“公共”路径
      final List<String> publicRoutes = [
        '/login',
        '/register',
        '/register/step2',
        '/forgot-password',
      ];

      // 检查当前路径是否在公共路径列表中
      final bool isPublicRoute = publicRoutes.any(
        (route) => location.startsWith(route),
      );

      // 处理加载状态 - 在认证状态初始化期间不进行重定向
      if (authStatus == AuthStatus.loading ||
          authStatus == AuthStatus.initial) {
        return null; // 保持当前路径，等待认证状态确定
      }

      final bool loggedIn = authStatus == AuthStatus.authenticated;

      // 规则 1: 如果已经登录，但要去公共页面（如登录页），则重定向到主页
      if (loggedIn && isPublicRoute) {
        return '/home';
      }

      // 规则 2: 如果没有登录，但要去一个非公共页面（需要保护的页面），则重定向到登录页
      if (!loggedIn && !isPublicRoute) {
        return '/login';
      }

      // 规则 3: 其他所有情况（已登录去保护页，未登录去公共页），都不进行重定向
      return null;
    },
    routes: [
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      // 这样 redirect 逻辑更容易判断
      GoRoute(
        path: '/register',
        name: 'registerStep1',
        builder: (context, state) => const RegisterStep1Page(),
        routes: [
          GoRoute(
            path: 'step2', // 相对路径，完整路径是 /register/step2
            name: 'registerStep2',
            builder: (context, state) {
              // 从 extra 中安全地获取参数
              final args = state.extra as Map<String, dynamic>?;
              final contact = args?['contact'] as String?;
              final verificationCode = args?['verificationCode'] as String?;
              // 如果参数丢失，可以显示错误页面或返回上一页
              if (contact == null || verificationCode == null) {
                return Scaffold(body: Center(child: Text("注册流程错误，缺少必要信息。")));
              }
              return RegisterStep2Page(
                contact: contact,
                verificationCode: verificationCode,
              ); // 仅为示例
            },
          ),
        ],
      ),
      StatefulShellRoute.indexedStack(
        builder: (context, state, navigationShell) =>
            BottomPage(navigationShell: navigationShell),
        branches: [
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/home',
                name: 'home',
                builder: (context, state) => const HomePage(),
                routes: [
                  // 交易详情页路由
                  GoRoute(
                    path: 'transaction/:transactionId',
                    name: 'transactionDetail',
                    builder: (context, state) {
                      final transactionId =
                          state.pathParameters['transactionId']!;
                      return TransactionDetailPage(
                        transactionId: transactionId,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/forecast',
                name: 'forecast',
                builder: (context, state) {
                  return const ForecastTabView();
                },
                routes: [
                  GoRoute(
                    path: 'onboarding',
                    name: 'forecast-onboarding',
                    builder: (context, state) => const ForecastOnboardingPage(),
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/ai',
                name: 'ai',
                builder: (context, state) {
                  return AIChatPage(conversationId: null);
                },
                routes: [
                  GoRoute(
                    path: ':conversationId',
                    name: 'conversation',
                    builder: (context, state) {
                      return AIChatPage(
                        conversationId: state.pathParameters['conversationId'],
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/report',
                name: 'report',
                builder: (context, state) => const ReportPage(),
              ),
            ],
          ),
          StatefulShellBranch(
            routes: [
              GoRoute(
                path: '/profile',
                name: 'profile',
                builder: (context, state) => const ProfilePage(),
                routes: [
                  GoRoute(
                    path: '/language',
                    name: 'language-settings',
                    builder: (context, state) => const LanguageSettingsPage(),
                  ),
                  GoRoute(
                    path: '/debug',
                    name: 'auth-debug',
                    builder: (context, state) => const AuthDebugPage(),
                  ),
                  GoRoute(
                    path: '/font-test',
                    name: 'font-test',
                    builder: (context, state) => const FontTestPage(),
                  )
                ],
              ),
            ],
          ),
        ],
      ),

      // 共享空间路由
      GoRoute(
        path: '/shared-space',
        name: 'sharedSpaceList',
        builder: (context, state) => const SharedSpaceListPage(),
        routes: [
          GoRoute(
            path: '/invite-success',
            name: 'inviteSuccess',
            builder: (context, state) {
              final space = state.extra as SharedSpace;
              return InviteSuccessPage(space: space);
            },
          ),
          GoRoute(
            path: '/:spaceId',
            name: 'sharedSpaceDetail',
            builder: (context, state) {
              final spaceId = state.pathParameters['spaceId']!;
              return SharedSpaceDetailPage(spaceId: spaceId);
            },
            routes: [
              GoRoute(
                path: '/settings',
                name: 'sharedSpaceSettings',
                builder: (context, state) {
                  final spaceId = state.pathParameters['spaceId']!;
                  final space = state.extra as SharedSpace?;
                  // TODO: 创建空间设置页面
                  return Scaffold(
                    appBar: AppBar(title: const Text('空间设置')),
                    body: const Center(child: Text('空间设置页面开发中...')),
                  );
                },
              ),
            ],
          ),
        ],
      ),

      // 手动记账页面路由
      GoRoute(
        path: '/manual-entry',
        name: 'manualEntry',
        builder: (context, state) {
          final type = state.uri.queryParameters['type'];
          final isExpense = type != 'income'; // 默认为支出
          return ManualEntryPage(initialIsExpense: isExpense);
        },
      ),

      // 通知路由
      GoRoute(
        path: '/notifications',
        name: 'notifications',
        builder: (context, state) => const NotificationListPage(),
      ),

      // 深度链接路由 - 加入空间
      GoRoute(
        path: '/join-space',
        name: 'joinSpace',
        redirect: (context, state) {
          final code = state.uri.queryParameters['code'];
          if (code != null) {
            // 重定向到共享空间列表页，并传递邀请码
            return '/shared-space?join_code=$code';
          }
          return '/shared-space';
        },
      ),
    ],
  );
});
