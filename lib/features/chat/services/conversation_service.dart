import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_expense_tracker/core/network/network_client.dart';
import 'package:flutter_expense_tracker/features/chat/models/conversation_info.dart';
import 'package:flutter_expense_tracker/features/chat/models/paginated_conversations.dart';
import 'package:flutter_expense_tracker/core/network/exceptions/app_exception.dart';
import 'package:flutter_expense_tracker/features/chat/models/conversation_detail.dart'; // 导入新模型
import '../../auth/providers/auth_provider.dart';
import '../models/chat_message.dart';
import 'package:flutter_expense_tracker/features/chat/models/paginated_messages.dart';

class ConversationService {
  final NetworkClient _networkClient;

  ConversationService(this._networkClient);

  /// 获取分页会话列表
  Future<PaginatedConversations> getConversationList({int page = 1, int perPage = 10}) async {
    log('ConversationService: Starting getConversationList API call for page $page...');
    try {
      final result = await _networkClient.request<PaginatedConversations>(
        '/chat/conversations',
        method: HttpMethod.get,
        queryParameters: {
          'page': page,
          'per_page': perPage,
        },
        fromJsonT: (json) {
          log('ConversationService: Raw API response type: ${json.runtimeType}');
          log('ConversationService: Raw API response: $json');

          if (json == null) {
            log('ConversationService: API returned null, returning empty paginated result');
            return const PaginatedConversations(
              data: [],
              meta: ConversationMeta(
                currentPage: 1,
                lastPage: 1,
                perPage: 10,
                total: 0,
                hasMore: false,
              ),
            );
          }

          if (json is Map<String, dynamic>) {
            log('ConversationService: Processing paginated conversation response...');
            try {
              return PaginatedConversations.fromJson(json);
            } catch (e) {
              log('ConversationService: Error parsing paginated conversations: $e');
              rethrow;
            }
          }

          throw DataParsingException("API /chat/conversations 期望返回对象，但收到 ${json.runtimeType}");
        },
      );
      log('ConversationService: getConversationList completed successfully for page $page');
      return result;
    } catch (e) {
      log('ConversationService: getConversationList failed with error: $e');
      rethrow;
    }
  }

  /// 获取简单会话列表（兼容性方法）
  Future<List<ConversationInfo>> getSimpleConversationList({int page = 1, int perPage = 10}) async {
    final paginatedResult = await getConversationList(page: page, perPage: perPage);
    return paginatedResult.data;
  }

  // 获取单个会话的详细信息（包含第一页消息）
  Future<ConversationDetail> getConversationDetail(String conversationId) async {
    return await _networkClient.request<ConversationDetail>(
      '/chat/conversations/$conversationId', // 假设这是获取详情的端点
      method: HttpMethod.get,
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          log('ConversationService: Raw API response: $json');
          final result = ConversationDetail.fromJson(json);
          log('ConversationService: Parsed ConversationDetail - Messages: ${result.messages.length}');
          return result;
        }
        throw DataParsingException("API /chat/conversations/$conversationId 期望返回Map，但收到 ${json.runtimeType}");
      },
    );
  }

  // 获取特定页码的消息 (用于加载更多)
  // 这个方法现在只返回 PaginatedMessages
  Future<PaginatedMessages> getConversationMessagesPage(
    String conversationId, {
    required int page, // page 是必需的
    int limit = 20,
  }) async {
    return await _networkClient.request<PaginatedMessages>(
      '/chat/conversations/$conversationId/messages', // 假设这是专门获取消息分页的端点
      method: HttpMethod.get,
      queryParameters: {'page': page, 'limit': limit},
      fromJsonT: (json) {
        if (json is Map<String, dynamic>) {
          return PaginatedMessages.fromJson(json);
        }
        throw DataParsingException("API .../messages 期望返回Map，但收到 ${json.runtimeType}");
      },
    );
  }
}

final conversationServiceProvider = Provider<ConversationService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return ConversationService(networkClient);
});

// Provider for fetching the conversation list
// 使用 autoDispose 并添加强制刷新机制
final conversationListProvider = FutureProvider.autoDispose<List<ConversationInfo>>((ref) async {
  // 依赖 authTokenProvider，确保登录后才获取
  final token = ref.watch(authTokenProvider); // 从 auth_provider 导入

  // 如果 token 为 null，等待一下再检查，或者直接调用 API（让网络层处理认证）
  if (token == null) {
    log('ConversationListProvider: Token is null, but proceeding with API call');
    // 不要直接返回空列表，而是让 API 调用处理认证问题
  }

  log('ConversationListProvider: Starting to fetch conversation list... (${DateTime.now()})');
  final conversationService = ref.watch(conversationServiceProvider);
  try {
    final result = await conversationService.getSimpleConversationList();
    log('ConversationListProvider: Successfully fetched ${result.length} conversations');
    // 添加详细的数据日志
    for (int i = 0; i < result.length && i < 3; i++) {
      log('ConversationListProvider: Conversation $i: ${result[i].id} - ${result[i].title}');
    }
    return result;
  } catch (e, stackTrace) {
    log('ConversationListProvider: Error fetching conversations: $e');
    log('ConversationListProvider: Stack trace: $stackTrace');
    rethrow;
  }
});

// 添加一个手动刷新的Provider
final conversationListRefreshProvider = StateProvider<int>((ref) => 0);

// 创建一个依赖刷新状态的Provider
final refreshableConversationListProvider = FutureProvider.autoDispose<List<ConversationInfo>>((ref) async {
  // 监听刷新状态，当状态改变时重新执行
  ref.watch(conversationListRefreshProvider);

  log('RefreshableConversationListProvider: Starting to fetch conversation list... (${DateTime.now()})');
  final conversationService = ref.watch(conversationServiceProvider);
  try {
    final result = await conversationService.getSimpleConversationList();
    log('RefreshableConversationListProvider: Successfully fetched ${result.length} conversations');
    return result;
  } catch (e, stackTrace) {
    log('RefreshableConversationListProvider: Error fetching conversations: $e');
    rethrow;
  }
});
