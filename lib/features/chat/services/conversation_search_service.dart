// features/chat/services/conversation_search_service.dart
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/conversation_search_state.dart';
import '/core/network/network_client.dart';

/// 会话搜索服务
class ConversationSearchService {
  final NetworkClient _networkClient;

  ConversationSearchService(this._networkClient);

  /// 搜索会话历史
  Future<List<ConversationSearchResult>> searchConversations(String query) async {
    if (query.trim().isEmpty) {
      // 如果查询为空，返回最近的会话历史
      return _getRecentConversations();
    } else {
      // 如果有查询关键词，执行搜索
      return _apiSearchConversations(query);
    }
  }

  /// 获取最近的会话历史（用于搜索模式初始显示）
  Future<List<ConversationSearchResult>> _getRecentConversations() async {
    try {
      // 获取最近的会话数据
      final response = await _networkClient.request<Map<String, dynamic>>(
        '/chat/conversations',
        method: HttpMethod.get,
        queryParameters: {'page': 1, 'per_page': 20},
        fromJsonT: (json) {
          if (json is Map<String, dynamic>) {
            return json;
          }
          throw Exception("API /chat/conversations 期望返回一个对象，但收到了 ${json.runtimeType}");
        },
      );

      final conversations = response['data'] as List<dynamic>? ?? [];
      return conversations.map((item) {
        final data = item as Map<String, dynamic>;
        return ConversationSearchResult(
          id: data['id'] as String,
          title: data['title'] as String,
          snippet: data['title'] as String, // 使用标题作为片段
          updatedAt: DateTime.parse(data['updatedAt'] as String),
          highlights: [], // 没有搜索关键词，所以没有高亮
        );
      }).toList();
    } catch (e) {
      throw Exception('获取会话历史失败: $e');
    }
  }

  /// 模拟搜索会话数据
  Future<List<ConversationSearchResult>> _mockSearchConversations(String query) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    // 模拟搜索结果数据
    final mockConversations = [
      {
        'id': '1',
        'title': 'PG SQL Group By',
        'content': 'text: 你好，我是智能记账理财助手，是一个专业的财务管理工具...',
        'updatedAt': DateTime.now().subtract(const Duration(hours: 2)),
      },
      {
        'id': '2',
        'title': '消费记录汇总',
        'content': 'Text( 你好，世界，style: TextStyle( font...',
        'updatedAt': DateTime.now().subtract(const Duration(hours: 5)),
      },
      {
        'id': '3',
        'title': '从零开始解',
        'content': '你好，我是第一性原理拆解引擎，请问你...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 1)),
      },
      {
        'id': '4',
        'title': '自动生成标题流程',
        'content': '小段字符串，比如 你好 或 ，请问你...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 2)),
      },
      {
        'id': '5',
        'title': 'PHP Heredoc 解析',
        'content': '<PROMPT> 你好. PROMPT; ...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 3)),
      },
      {
        'id': '6',
        'title': 'Apifox 流式响应设置',
        'content': 'txt data: 你好 data: 这是类段应 data: [DO...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 4)),
      },
      {
        'id': '7',
        'title': 'UUID vs Snowflake对比',
        'content': '"data": 你好，我是的AI助手，data: {...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 5)),
      },
      {
        'id': '8',
        'title': '工具提示词格式化',
        'content': '这或许常像 (如 你好，你准，今天如...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 6)),
      },
      {
        'id': '9',
        'title': 'SSE 流式响应格式',
        'content': 'content": 你好，index:0,"finish_reason":n...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 7)),
      },
      {
        'id': '10',
        'title': 'Flutter SSE 内容合并',
        'content': '，data= 你好，我是AI助手，...',
        'updatedAt': DateTime.now().subtract(const Duration(days: 8)),
      },
    ];

    // 过滤匹配查询的会话
    final filteredConversations = mockConversations.where((conv) {
      final title = conv['title'] as String;
      final content = conv['content'] as String;
      final queryLower = query.toLowerCase();
      
      return title.toLowerCase().contains(queryLower) || 
             content.toLowerCase().contains(queryLower);
    }).toList();

    // 转换为搜索结果对象
    return filteredConversations.map((conv) {
      final title = conv['title'] as String;
      final content = conv['content'] as String;
      final queryLower = query.toLowerCase();
      
      // 生成高亮范围
      final highlights = <HighlightRange>[];
      
      // 在标题中查找匹配
      final titleLower = title.toLowerCase();
      int titleIndex = titleLower.indexOf(queryLower);
      if (titleIndex != -1) {
        highlights.add(HighlightRange(
          start: titleIndex,
          end: titleIndex + query.length,
          field: 'title',
        ));
      }
      
      // 在内容中查找匹配
      final contentLower = content.toLowerCase();
      int contentIndex = contentLower.indexOf(queryLower);
      if (contentIndex != -1) {
        highlights.add(HighlightRange(
          start: contentIndex,
          end: contentIndex + query.length,
          field: 'snippet',
        ));
      }
      
      // 生成内容片段（截取匹配周围的文本）
      String snippet = content;
      if (contentIndex != -1) {
        final start = (contentIndex - 20).clamp(0, content.length);
        final end = (contentIndex + query.length + 20).clamp(0, content.length);
        snippet = content.substring(start, end);
        if (start > 0) snippet = '...$snippet';
        if (end < content.length) snippet = '$snippet...';
        
        // 调整高亮范围以匹配片段
        if (highlights.isNotEmpty && highlights.last.field == 'snippet') {
          highlights.removeLast();
          final adjustedStart = contentIndex - start;
          highlights.add(HighlightRange(
            start: adjustedStart,
            end: adjustedStart + query.length,
            field: 'snippet',
          ));
        }
      }
      
      return ConversationSearchResult(
        id: conv['id'] as String,
        title: title,
        snippet: snippet,
        updatedAt: conv['updatedAt'] as DateTime,
        highlights: highlights,
      );
    }).toList();
  }

  /// 真实的API搜索实现（待实现）
  Future<List<ConversationSearchResult>> _apiSearchConversations(String query) async {
    try {
      final response = await _networkClient.request<Map<String, dynamic>>(
        '/conversations/search',
        method: HttpMethod.get,
        queryParameters: {'q': query, 'limit': 20},
        fromJsonT: (json) {
          if (json is Map<String, dynamic>) {
            return json;
          }
          throw Exception("API /conversations/search 期望返回一个对象，但收到了 ${json.runtimeType}");
        },
      );

      final results = response['data'] as List<dynamic>? ?? [];
      return results.map((item) {
        final data = item as Map<String, dynamic>;
        return ConversationSearchResult(
          id: data['id'] as String,
          title: data['title'] as String,
          snippet: data['snippet'] as String,
          updatedAt: DateTime.parse(data['updated_at'] as String),
          highlights: (data['highlights'] as List<dynamic>? ?? [])
              .map((h) => HighlightRange(
                    start: h['start'] as int,
                    end: h['end'] as int,
                    field: h['field'] as String,
                  ))
              .toList(),
        );
      }).toList();
    } catch (e) {
      throw Exception('搜索会话失败: $e');
    }
  }
}

/// 会话搜索服务提供者
final conversationSearchServiceProvider = Provider<ConversationSearchService>((ref) {
  final networkClient = ref.watch(networkClientProvider);
  return ConversationSearchService(networkClient);
});
