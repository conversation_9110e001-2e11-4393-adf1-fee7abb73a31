import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:forui/forui.dart';
import 'package:intl/intl.dart';
import '../providers/chat_history_notifier.dart';
import '../providers/conversation_search_notifier.dart';
import '../providers/conversation_search_state.dart';
import '../providers/paginated_conversation_notifier.dart';
import '../models/conversation_info.dart';
import '../services/conversation_service.dart';
import 'conversation_item_skeleton.dart';

class ChatConversationDrawer extends ConsumerStatefulWidget {
  const ChatConversationDrawer({super.key});

  @override
  ConsumerState<ChatConversationDrawer> createState() => _ChatConversationDrawerState();
}

class _ChatConversationDrawerState extends ConsumerState<ChatConversationDrawer> {
  @override
  void initState() {
    super.initState();
    // 确保在初始化时立即加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final paginatedState = ref.read(paginatedConversationProvider);
      if (paginatedState.conversations.isEmpty && !paginatedState.isLoading) {
        ref.read(paginatedConversationProvider.notifier).loadFirstPage();
      }
    });
  }

  // 构建骨架屏列表
  Widget _buildSkeletonList(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: 8,
      separatorBuilder: (context, index) => const SizedBox(height: 4),
      itemBuilder: (context, index) {
        return const ConversationItemSkeleton();
      },
    );
  }

  // 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    final theme = context.theme;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: theme.colors.muted,
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                FIcons.messageCircle,
                size: 32,
                color: theme.colors.mutedForeground,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '没有历史会话',
              style: theme.typography.base.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colors.foreground,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '开始新的对话吧！',
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // 构建错误状态
  Widget _buildErrorState(BuildContext context, Object error, WidgetRef ref) {
    final theme = context.theme;
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                color: theme.colors.destructive.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                FIcons.x,
                size: 32,
                color: theme.colors.destructive,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '加载失败',
              style: theme.typography.base.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colors.destructive,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: theme.typography.sm.copyWith(
                color: theme.colors.mutedForeground,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: theme.colors.border),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () {
                    ref.invalidate(conversationListProvider);
                  },
                  borderRadius: BorderRadius.circular(8),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Text(
                      '重试',
                      style: theme.typography.sm.copyWith(
                        color: theme.colors.foreground,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建会话列表
  Widget _buildConversationList(BuildContext context, List<dynamic> conversations, String? currentConversationId, WidgetRef ref) {
    final theme = context.theme;
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: conversations.length,
      separatorBuilder: (context, index) => const SizedBox(height: 4),
      itemBuilder: (context, index) {
        final conversation = conversations[index];
        final isSelected = conversation.id == currentConversationId;

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          decoration: BoxDecoration(
            color: isSelected
                ? theme.colors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            border: isSelected
                ? Border.all(color: theme.colors.primary.withValues(alpha: 0.2))
                : null,
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                // 如果点击的不是当前会话
                if (!isSelected) {
                  context.goNamed(
                    'conversation',
                    pathParameters: {
                      'conversationId': conversation.id,
                    },
                  );
                }
                // 关闭抽屉
                Navigator.of(context).pop();
              },
              borderRadius: BorderRadius.circular(8),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Row(
                  children: [
                    // 会话图标
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? theme.colors.primary.withValues(alpha: 0.2)
                            : theme.colors.muted,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        FIcons.messageCircle,
                        size: 16,
                        color: isSelected
                            ? theme.colors.primary
                            : theme.colors.mutedForeground,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 会话信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            conversation.title,
                            style: theme.typography.sm.copyWith(
                              fontWeight: FontWeight.w500,
                              color: isSelected
                                  ? theme.colors.primary
                                  : theme.colors.foreground,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            DateFormat('M月d日 HH:mm', 'zh_CN').format(conversation.updatedAt),
                            style: theme.typography.xs.copyWith(
                              color: theme.colors.mutedForeground,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = context.theme;
    final paginatedState = ref.watch(paginatedConversationProvider);
    final currentConversationId = ref.watch(chatHistoryProvider.select((state) => state.currentConversationId));
    final searchState = ref.watch(conversationSearchProvider);



    return Drawer(
      backgroundColor: theme.colors.background,
      shape: const RoundedRectangleBorder(),
      child: SafeArea(
        child: searchState.mode == SearchMode.search
            ? _buildSearchMode(context, ref, theme)
            : _buildNormalMode(context, ref, theme, paginatedState, currentConversationId),
      ),
    );
  }

  // 正常模式界面
  Widget _buildNormalMode(
    BuildContext context,
    WidgetRef ref,
    FThemeData theme,
    PaginatedConversationState paginatedState,
    String? currentConversationId,
  ) {
    return Column(
      children: [
        // 顶部：搜索框和新建聊天按钮
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // 搜索框
              GestureDetector(
                onTap: () {
                  ref.read(conversationSearchProvider.notifier).enterSearchMode();
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                  decoration: BoxDecoration(
                    border: Border.all(color: theme.colors.border),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        FIcons.search,
                        size: 16,
                        color: theme.colors.mutedForeground,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '你好',
                        style: theme.typography.sm.copyWith(
                          color: theme.colors.mutedForeground,
                        ),
                      ),
                      const Spacer(),
                      // 新建会话按钮
                      GestureDetector(
                        onTap: () {
                          ref.read(chatHistoryProvider.notifier).createNewConversation();
                          Navigator.of(context).pop();
                          context.go('/ai');
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: theme.colors.muted,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            FIcons.plus,
                            size: 14,
                            color: theme.colors.mutedForeground,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
              // 两列布局：财务助手和库
              Row(
                children: [
                  Expanded(
                    child: _buildCategoryColumn(
                      context,
                      theme,
                      'ChatGPT',
                      FIcons.messageCircle,
                      () {
                        // 点击财务助手
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildCategoryColumn(
                      context,
                      theme,
                      '库',
                      FIcons.database,
                      () {
                        // 点击库
                      },
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // 中间：会话列表
        Expanded(
          child: _buildPaginatedConversationList(context, ref, theme, paginatedState, currentConversationId),
        ),
        // 底部：用户信息
        Container(
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: theme.colors.border),
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () {
                context.go('/profile');
                Navigator.of(context).pop();
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: theme.colors.primary,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        FIcons.user,
                        size: 16,
                        color: theme.colors.primaryForeground,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '老计 伏枥',
                            style: theme.typography.sm.copyWith(
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            '查看个人资料',
                            style: theme.typography.xs.copyWith(
                              color: theme.colors.mutedForeground,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      FIcons.chevronRight,
                      size: 16,
                      color: theme.colors.mutedForeground,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 构建分类列
  Widget _buildCategoryColumn(
    BuildContext context,
    FThemeData theme,
    String title,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          border: Border.all(color: theme.colors.border),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 16,
              color: theme.colors.foreground,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: theme.typography.sm.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colors.foreground,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建分页会话列表
  Widget _buildPaginatedConversationList(
    BuildContext context,
    WidgetRef ref,
    FThemeData theme,
    PaginatedConversationState paginatedState,
    String? currentConversationId,
  ) {
    if (paginatedState.isLoading && paginatedState.conversations.isEmpty) {
      return _buildSkeletonList(context);
    }

    if (paginatedState.error != null && paginatedState.conversations.isEmpty) {
      return _buildErrorState(context, paginatedState.error!, ref);
    }

    if (paginatedState.conversations.isEmpty) {
      return _buildEmptyState(context);
    }

    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        // 当滚动到底部时加载更多
        if (scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            paginatedState.hasMore &&
            !paginatedState.isLoadingMore) {
          ref.read(paginatedConversationProvider.notifier).loadNextPage();
        }
        return false;
      },
      child: RefreshIndicator(
        onRefresh: () async {
          await ref.read(paginatedConversationProvider.notifier).refresh();
        },
        child: ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          itemCount: paginatedState.conversations.length + (paginatedState.hasMore ? 1 : 0),
          itemBuilder: (context, index) {
            // 如果是最后一项且还有更多数据，显示加载指示器
            if (index == paginatedState.conversations.length) {
              return Padding(
                padding: const EdgeInsets.all(16),
                child: Center(
                  child: paginatedState.isLoadingMore
                      ? const CircularProgressIndicator()
                      : const SizedBox.shrink(),
                ),
              );
            }

            final conversation = paginatedState.conversations[index];
            return _buildSingleConversationItem(context, conversation, currentConversationId, ref);
          },
        ),
      ),
    );
  }

  // 构建单个会话项
  Widget _buildSingleConversationItem(
    BuildContext context,
    ConversationInfo conversation,
    String? currentConversationId,
    WidgetRef ref,
  ) {
    final theme = context.theme;
    final isSelected = conversation.id == currentConversationId;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        color: isSelected
            ? theme.colors.primary.withValues(alpha: 0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: isSelected
            ? Border.all(color: theme.colors.primary.withValues(alpha: 0.2))
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (!isSelected) {
              context.goNamed(
                'conversation',
                pathParameters: {
                  'conversationId': conversation.id,
                },
              );
            }
            Navigator.of(context).pop();
          },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                // 会话图标
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? theme.colors.primary.withValues(alpha: 0.2)
                        : theme.colors.muted,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    FIcons.messageCircle,
                    size: 16,
                    color: isSelected
                        ? theme.colors.primary
                        : theme.colors.mutedForeground,
                  ),
                ),
                const SizedBox(width: 12),
                // 会话信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        conversation.title,
                        style: theme.typography.sm.copyWith(
                          fontWeight: FontWeight.w500,
                          color: isSelected
                              ? theme.colors.primary
                              : theme.colors.foreground,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        DateFormat('M月d日 HH:mm', 'zh_CN').format(conversation.updatedAt),
                        style: theme.typography.xs.copyWith(
                          color: theme.colors.mutedForeground,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 搜索模式界面
  Widget _buildSearchMode(BuildContext context, WidgetRef ref, FThemeData theme) {
    final searchState = ref.watch(conversationSearchProvider);

    return Column(
      children: [
        // 顶部：搜索框和取消按钮
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: _SearchTextField(
                  searchState: searchState,
                  onChanged: (value) {
                    ref.read(conversationSearchProvider.notifier).updateQuery(value);
                  },
                  onClear: () {
                    ref.read(conversationSearchProvider.notifier).clearSearch();
                  },
                ),
              ),
              const SizedBox(width: 12),
              GestureDetector(
                onTap: () {
                  ref.read(conversationSearchProvider.notifier).exitSearchMode();
                },
                child: Text(
                  '取消',
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        // 搜索结果区域
        Expanded(
          child: _buildSearchResults(context, ref, theme, searchState),
        ),
      ],
    );
  }

  // 构建搜索结果
  Widget _buildSearchResults(
    BuildContext context,
    WidgetRef ref,
    FThemeData theme,
    ConversationSearchState searchState,
  ) {
    if (searchState.isLoading) {
      return _buildSkeletonList(context);
    }

    if (searchState.error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FIcons.triangleAlert,
                size: 48,
                color: theme.colors.destructive,
              ),
              const SizedBox(height: 16),
              Text(
                '搜索失败',
                style: theme.typography.base.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colors.destructive,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                searchState.error!,
                style: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    if (!searchState.hasSearched && searchState.results.isEmpty) {
      return _buildSearchSuggestions(context, theme);
    }

    if (searchState.results.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                FIcons.search,
                size: 48,
                color: theme.colors.mutedForeground.withValues(alpha: 0.5),
              ),
              const SizedBox(height: 16),
              Text(
                '未找到相关会话',
                style: theme.typography.base.copyWith(
                  color: theme.colors.mutedForeground,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '尝试使用其他关键词搜索',
                style: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: searchState.results.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final result = searchState.results[index];
        return _buildSearchResultItem(context, ref, theme, result, searchState.query);
      },
    );
  }

  // 构建搜索建议
  Widget _buildSearchSuggestions(BuildContext context, FThemeData theme) {
    final suggestions = [
      {'icon': FIcons.hash, 'title': '第一性原理 拆解引擎'},
      {'icon': FIcons.terminal, 'title': 'Linux ubuntu 命令大师'},
      {'icon': FIcons.circle, 'title': 'Laravel GPT'},
      {'icon': FIcons.code, 'title': '技术指导大师'},
    ];

    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: suggestions.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final suggestion = suggestions[index];
        return Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                suggestion['icon'] as IconData,
                size: 20,
                color: theme.colors.mutedForeground,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  suggestion['title'] as String,
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.foreground,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 构建搜索结果项
  Widget _buildSearchResultItem(
    BuildContext context,
    WidgetRef ref,
    FThemeData theme,
    ConversationSearchResult result,
    String query,
  ) {
    return GestureDetector(
      onTap: () {
        // 导航到会话
        context.goNamed(
          'conversation',
          pathParameters: {'conversationId': result.id},
        );
        Navigator.of(context).pop();
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: theme.colors.border.withValues(alpha: 0.5)),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题（带高亮）
            _buildHighlightedText(
              result.title,
              query,
              result.highlights.where((h) => h.field == 'title').toList(),
              theme.typography.sm.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colors.foreground,
              ),
              theme.colors.primary.withValues(alpha: 0.2),
            ),
            const SizedBox(height: 4),
            // 内容片段（带高亮）
            _buildHighlightedText(
              result.snippet,
              query,
              result.highlights.where((h) => h.field == 'snippet').toList(),
              theme.typography.xs.copyWith(
                color: theme.colors.mutedForeground,
              ),
              theme.colors.primary.withValues(alpha: 0.2),
            ),
            const SizedBox(height: 4),
            // 时间
            Text(
              DateFormat('M月d日 HH:mm', 'zh_CN').format(result.updatedAt),
              style: theme.typography.xs.copyWith(
                color: theme.colors.mutedForeground.withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建高亮文本
  Widget _buildHighlightedText(
    String text,
    String query,
    List<HighlightRange> highlights,
    TextStyle baseStyle,
    Color highlightColor,
  ) {
    if (highlights.isEmpty || query.isEmpty) {
      return Text(text, style: baseStyle);
    }

    final spans = <TextSpan>[];
    int currentIndex = 0;

    for (final highlight in highlights) {
      // 添加高亮前的文本
      if (currentIndex < highlight.start) {
        spans.add(TextSpan(
          text: text.substring(currentIndex, highlight.start),
          style: baseStyle,
        ));
      }

      // 添加高亮文本
      spans.add(TextSpan(
        text: text.substring(highlight.start, highlight.end),
        style: baseStyle.copyWith(
          backgroundColor: highlightColor,
          fontWeight: FontWeight.w600,
        ),
      ));

      currentIndex = highlight.end;
    }

    // 添加剩余文本
    if (currentIndex < text.length) {
      spans.add(TextSpan(
        text: text.substring(currentIndex),
        style: baseStyle,
      ));
    }

    return RichText(
      text: TextSpan(children: spans),
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }
}

/// 自定义搜索文本框组件
class _SearchTextField extends StatefulWidget {
  final ConversationSearchState searchState;
  final ValueChanged<String> onChanged;
  final VoidCallback onClear;

  const _SearchTextField({
    required this.searchState,
    required this.onChanged,
    required this.onClear,
  });

  @override
  State<_SearchTextField> createState() => _SearchTextFieldState();
}

class _SearchTextFieldState extends State<_SearchTextField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.searchState.query);
  }

  @override
  void didUpdateWidget(_SearchTextField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.searchState.query != _controller.text) {
      _controller.text = widget.searchState.query;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = context.theme;

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.colors.border),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // 搜索图标
          Padding(
            padding: const EdgeInsets.only(left: 12),
            child: Icon(
              FIcons.search,
              size: 16,
              color: theme.colors.mutedForeground,
            ),
          ),
          // 输入框
          Expanded(
            child: TextField(
              controller: _controller,
              autofocus: true,
              style: theme.typography.sm,
              decoration: InputDecoration(
                hintText: '搜索',
                hintStyle: theme.typography.sm.copyWith(
                  color: theme.colors.mutedForeground,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 10,
                ),
              ),
              onChanged: widget.onChanged,
            ),
          ),
          // 清除按钮
          if (_controller.text.isNotEmpty)
            GestureDetector(
              onTap: () {
                _controller.clear();
                widget.onClear();
              },
              child: Padding(
                padding: const EdgeInsets.only(right: 12),
                child: Icon(
                  FIcons.x,
                  size: 16,
                  color: theme.colors.mutedForeground,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
