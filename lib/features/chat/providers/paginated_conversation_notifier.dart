// features/chat/providers/paginated_conversation_notifier.dart
import 'dart:developer';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/conversation_info.dart';
import '../models/paginated_conversations.dart';
import '../services/conversation_service.dart';

part 'paginated_conversation_notifier.freezed.dart';

/// 分页会话列表状态
@freezed
abstract class PaginatedConversationState with _$PaginatedConversationState {
  const factory PaginatedConversationState({
    @Default([]) List<ConversationInfo> conversations,
    @Default(1) int currentPage,
    @Default(false) bool isLoading,
    @Default(false) bool isLoadingMore,
    @Default(true) bool hasMore,
    @Default(10) int perPage,
    @Default(0) int total,
    String? error,
  }) = _PaginatedConversationState;
}

/// 分页会话列表状态管理
class PaginatedConversationNotifier extends StateNotifier<PaginatedConversationState> {
  final ConversationService _conversationService;

  PaginatedConversationNotifier(this._conversationService) : super(const PaginatedConversationState());

  /// 加载第一页数据
  Future<void> loadFirstPage() async {
    if (state.isLoading) return;

    state = state.copyWith(
      isLoading: true,
      error: null,
      conversations: [],
      currentPage: 1,
    );

    try {
      final result = await _conversationService.getConversationList(page: 1, perPage: state.perPage);
      
      state = state.copyWith(
        conversations: result.data,
        currentPage: result.meta.currentPage,
        hasMore: result.meta.hasMore,
        total: result.meta.total,
        isLoading: false,
      );
      
      log('PaginatedConversationNotifier: Loaded first page with ${result.data.length} conversations');
    } catch (e) {
      log('PaginatedConversationNotifier: Error loading first page: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 加载下一页数据
  Future<void> loadNextPage() async {
    if (state.isLoadingMore || !state.hasMore || state.isLoading) return;

    state = state.copyWith(isLoadingMore: true, error: null);

    try {
      final nextPage = state.currentPage + 1;
      final result = await _conversationService.getConversationList(page: nextPage, perPage: state.perPage);
      
      state = state.copyWith(
        conversations: [...state.conversations, ...result.data],
        currentPage: result.meta.currentPage,
        hasMore: result.meta.hasMore,
        total: result.meta.total,
        isLoadingMore: false,
      );
      
      log('PaginatedConversationNotifier: Loaded page $nextPage with ${result.data.length} conversations');
    } catch (e) {
      log('PaginatedConversationNotifier: Error loading next page: $e');
      state = state.copyWith(
        isLoadingMore: false,
        error: e.toString(),
      );
    }
  }

  /// 刷新数据
  Future<void> refresh() async {
    await loadFirstPage();
  }

  /// 重置状态
  void reset() {
    state = const PaginatedConversationState();
  }
}

/// 分页会话列表提供者
final paginatedConversationProvider = StateNotifierProvider<PaginatedConversationNotifier, PaginatedConversationState>((ref) {
  final conversationService = ref.watch(conversationServiceProvider);
  return PaginatedConversationNotifier(conversationService);
});
