// features/chat/providers/conversation_search_state.dart
import 'package:freezed_annotation/freezed_annotation.dart';

part 'conversation_search_state.freezed.dart';

/// 搜索模式枚举
enum SearchMode {
  /// 正常模式 - 显示会话列表和分类
  normal,
  /// 搜索模式 - 显示搜索界面
  search,
}

/// 会话搜索结果项
@freezed
abstract class ConversationSearchResult with _$ConversationSearchResult {
  const factory ConversationSearchResult({
    required String id,
    required String title,
    required String snippet, // 搜索匹配的内容片段
    required DateTime updatedAt,
    required List<HighlightRange> highlights, // 高亮范围
  }) = _ConversationSearchResult;
}

/// 高亮范围
@freezed
abstract class HighlightRange with _$HighlightRange {
  const factory HighlightRange({
    required int start,
    required int end,
    required String field, // 'title' 或 'snippet'
  }) = _HighlightRange;
}

/// 会话搜索状态
@freezed
abstract class ConversationSearchState with _$ConversationSearchState {
  const factory ConversationSearchState({
    @Default(SearchMode.normal) SearchMode mode,
    @Default('') String query,
    @Default([]) List<ConversationSearchResult> results,
    @Default(false) bool isLoading,
    String? error,
    @Default(false) bool hasSearched, // 是否已经进行过搜索
  }) = _ConversationSearchState;
}
