// features/chat/providers/conversation_search_notifier.dart
import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'conversation_search_state.dart';
import '../services/conversation_search_service.dart';

/// 会话搜索状态管理
class ConversationSearchNotifier extends StateNotifier<ConversationSearchState> {
  final ConversationSearchService _searchService;
  Timer? _debounceTimer;

  ConversationSearchNotifier(this._searchService) : super(const ConversationSearchState());

  /// 进入搜索模式
  void enterSearchMode() {
    state = state.copyWith(
      mode: SearchMode.search,
      query: '',
      results: [],
      error: null,
      hasSearched: false,
    );

    // 自动加载最近的会话历史
    _loadRecentConversations();
  }

  /// 加载最近的会话历史
  Future<void> _loadRecentConversations() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final results = await _searchService.searchConversations(''); // 空查询获取最近会话
      state = state.copyWith(
        results: results,
        isLoading: false,
        hasSearched: false, // 这不算真正的搜索
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
        hasSearched: false,
      );
    }
  }

  /// 退出搜索模式
  void exitSearchMode() {
    _debounceTimer?.cancel();
    state = state.copyWith(
      mode: SearchMode.normal,
      query: '',
      results: [],
      error: null,
      hasSearched: false,
      isLoading: false,
    );
  }

  /// 更新搜索查询（带防抖）
  void updateQuery(String query) {
    state = state.copyWith(query: query);
    
    // 取消之前的防抖定时器
    _debounceTimer?.cancel();
    
    if (query.trim().isEmpty) {
      // 如果查询为空，清空结果
      state = state.copyWith(
        results: [],
        error: null,
        hasSearched: false,
        isLoading: false,
      );
      return;
    }

    // 设置防抖定时器
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _performSearch(query.trim());
    });
  }

  /// 执行搜索
  Future<void> _performSearch(String query) async {
    if (query.isEmpty) return;

    state = state.copyWith(isLoading: true, error: null);

    try {
      final results = await _searchService.searchConversations(query);
      state = state.copyWith(
        results: results,
        isLoading: false,
        hasSearched: true,
      );
    } catch (e) {
      state = state.copyWith(
        error: e.toString(),
        isLoading: false,
        hasSearched: true,
      );
    }
  }

  /// 清空搜索
  void clearSearch() {
    _debounceTimer?.cancel();
    state = state.copyWith(
      query: '',
      results: [],
      error: null,
      hasSearched: false,
      isLoading: false,
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    super.dispose();
  }
}

/// 会话搜索状态提供者
final conversationSearchProvider = StateNotifierProvider<ConversationSearchNotifier, ConversationSearchState>((ref) {
  final searchService = ref.watch(conversationSearchServiceProvider);
  return ConversationSearchNotifier(searchService);
});
