import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:forui/forui.dart';
import 'package:go_router/go_router.dart';
import '../../../shared/config/category_config.dart';

import '../widgets/manual_entry/custom_number_keyboard.dart';
import '../widgets/manual_entry/tag_input_field.dart';
import '../providers/manual_entry_provider.dart';

/// 手动记账页面
/// 独立页面，支持Tab切换支出/收入，使用自定义数字键盘
class ManualEntryPage extends ConsumerStatefulWidget {
  final bool initialIsExpense;

  const ManualEntryPage({super.key, this.initialIsExpense = true});

  @override
  ConsumerState<ManualEntryPage> createState() => _ManualEntryPageState();
}

class _ManualEntryPageState extends ConsumerState<ManualEntryPage>
    with TickerProviderStateMixin {
  late NumberKeyboardController _keyboardController;

  final TextEditingController _descriptionController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // 支出数据
  String? _expenseCategoryId;
  List<String> _expenseTags = [];

  // 收入数据
  String? _incomeCategoryId;
  List<String> _incomeTags = [];

  // 共享数据
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = const TimeOfDay(hour: 0, minute: 0);
  bool _useQuickDate = true;
  String _quickDateType = 'today'; // today, yesterday, dayBeforeYesterday
  int _currentTabIndex = 0;

  // 用于存储Builder的context，以便正确显示Toast
  BuildContext? _builderContext;

  @override
  void initState() {
    super.initState();
    _currentTabIndex = widget.initialIsExpense ? 0 : 1;
    _keyboardController = NumberKeyboardController();

    // 设置默认分类
    _setDefaultCategory();
  }

  @override
  void dispose() {
    _keyboardController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  // 获取当前tab的类型
  String get _currentType => _currentTabIndex == 0 ? 'expense' : 'income';
  bool get _isExpense => _currentTabIndex == 0;

  // 获取当前选中的分类ID
  String? get _selectedCategoryId =>
      _isExpense ? _expenseCategoryId : _incomeCategoryId;

  // 设置当前选中的分类ID
  set _selectedCategoryId(String? value) {
    if (_isExpense) {
      _expenseCategoryId = value;
    } else {
      _incomeCategoryId = value;
    }
  }

  // 获取当前的标签列表
  List<String> get _tags => _isExpense ? _expenseTags : _incomeTags;

  // 设置当前的标签列表
  set _tags(List<String> value) {
    if (_isExpense) {
      _expenseTags = value;
    } else {
      _incomeTags = value;
    }
  }

  void _setDefaultCategory() {
    if (_isExpense) {
      // 支出默认选择第一个非收入分类
      if (_expenseCategoryId == null) {
        final categories = CategoryConfig.getAllCategories()
            .where((cat) => cat.id != '6')
            .toList();
        if (categories.isNotEmpty) {
          _expenseCategoryId = categories.first.id;
        }
      }
    } else {
      // 收入固定选择ID为6的分类
      _incomeCategoryId ??= '6';
    }
  }

  @override
  Widget build(BuildContext context) {
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;

    return FScaffold(
      // 使用FHeader.nested，专为子页面设计
      header: FHeader.nested(
        title: const Text('记账'),
        prefixes: [FHeaderAction.back(onPress: () => context.pop())],
      ),
      // 启用键盘避让
      resizeToAvoidBottomInset: true,
      // 保存Builder的context用于Toast显示
      child: Builder(
        builder: (context) {
          _builderContext = context;

          return Column(
            children: [
              // Tab栏
              _buildTabBar(context),

              // 主要内容区域
              Expanded(
                child: _currentTabIndex == 0
                    ? _buildExpenseTab(context)
                    : _buildIncomeTab(context),
              ),

              // 金额显示和数字键盘区域
              // 当系统键盘弹出时，这个区域会被推上去或隐藏
              if (keyboardHeight == 0) ...[
                // 金额显示区域（移动到数字键盘上方）
                _buildAmountDisplay(context),

                // 数字键盘
                CustomNumberKeyboard(
                  onKeyTap: _keyboardController.onKeyTap,
                  onDeleteTap: _keyboardController.onDeleteTap,
                  onConfirmTap: () => _handleSubmit(),
                  confirmText: '保存记账',
                ),
              ] else ...[
                // 当系统键盘弹出时，只显示简化的金额显示
                _buildCompactAmountDisplay(context),
              ],
            ],
          );
        },
      ),
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return FTabs(
      initialIndex: widget.initialIsExpense ? 0 : 1,
      onPress: (index) {
        setState(() {
          _currentTabIndex = index;
          _setDefaultCategory();
        });
      },
      children: [
        FTabEntry(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(FIcons.arrowUp, size: 16, color: const Color(0xFFEF4444)),
              const SizedBox(width: 4),
              const Text('支出'),
            ],
          ),
          child: Container(),
        ),
        FTabEntry(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(FIcons.arrowDown, size: 16, color: const Color(0xFF10B981)),
              const SizedBox(width: 4),
              const Text('收入'),
            ],
          ),
          child: Container(),
        ),
      ],
    );
  }

  Widget _buildExpenseTab(BuildContext context) {
    return _buildFormContent(context, isExpense: true);
  }

  Widget _buildIncomeTab(BuildContext context) {
    return _buildFormContent(context, isExpense: false);
  }

  Widget _buildFormContent(BuildContext context, {required bool isExpense}) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 分类选择（收入时不显示）
            if (isExpense) ...[
              _buildCategorySelector(context),
              const SizedBox(height: 24),
            ],

            // 日期时间选择
            _buildDateTimeSelector(context),
            const SizedBox(height: 24),

            // 描述输入
            _buildDescriptionField(context),
            const SizedBox(height: 24),

            // 标签输入
            _buildTagsField(context),
            const SizedBox(height: 16), // 减少底部间距
          ],
        ),
      ),
    );
  }

  Widget _buildCategorySelector(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    // 获取支出分类（排除收入分类ID=6）
    final categories = CategoryConfig.getAllCategories()
        .where((cat) => cat.id != '6')
        .toList();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '分类',
          style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        // 使用水平滚动的分类选择器，更符合记账APP的设计
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = _selectedCategoryId == category.id;

              return Padding(
                padding: EdgeInsets.only(
                  left: index == 0 ? 0 : 8,
                  right: index == categories.length - 1 ? 0 : 8,
                ),
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategoryId = category.id;
                    });
                  },
                  child: Column(
                    children: [
                      Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: isSelected ? colors.primary : colors.secondary,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected ? colors.primary : colors.border,
                            width: 1.5,
                          ),
                        ),
                        child: Icon(
                          category.icon,
                          size: 20,
                          color: isSelected
                              ? colors.primaryForeground
                              : colors.foreground,
                        ),
                      ),
                      const SizedBox(height: 4),
                      SizedBox(
                        width: 48,
                        child: Text(
                          category.name,
                          style: theme.typography.xs.copyWith(
                            color: isSelected
                                ? colors.primary
                                : colors.mutedForeground,
                            fontWeight: isSelected
                                ? FontWeight.w600
                                : FontWeight.w400,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildDateTimeSelector(BuildContext context) {
    final theme = context.theme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '日期时间',
          style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),

        // 快速日期选择
        Row(
          children: [
            _buildQuickDateButton('今天', 'today'),
            const SizedBox(width: 8),
            _buildQuickDateButton('昨天', 'yesterday'),
            const SizedBox(width: 8),
            _buildQuickDateButton('前天', 'dayBeforeYesterday'),
          ],
        ),
        const SizedBox(height: 12),

        // 时间选择按钮
        GestureDetector(
          onTap: _showDateTimePicker,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              border: Border.all(color: theme.colors.border),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  FIcons.clock,
                  size: 16,
                  color: theme.colors.mutedForeground,
                ),
                const SizedBox(width: 8),
                Text(
                  _formatDateTime(_selectedDate, _selectedTime),
                  style: theme.typography.sm.copyWith(
                    color: theme.colors.foreground,
                  ),
                ),
                const Spacer(),
                Icon(
                  FIcons.chevronDown,
                  size: 16,
                  color: theme.colors.mutedForeground,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuickDateButton(String label, String type) {
    final isSelected = _useQuickDate && _quickDateType == type;

    return Expanded(
      child: FButton(
        style: isSelected ? FButtonStyle.primary() : FButtonStyle.outline(),
        onPress: () {
          setState(() {
            _useQuickDate = true;
            _quickDateType = type;

            // 设置对应的日期，时间默认为00:00
            switch (type) {
              case 'today':
                _selectedDate = DateTime.now();
                break;
              case 'yesterday':
                _selectedDate = DateTime.now().subtract(
                  const Duration(days: 1),
                );
                break;
              case 'dayBeforeYesterday':
                _selectedDate = DateTime.now().subtract(
                  const Duration(days: 2),
                );
                break;
            }
            _selectedTime = const TimeOfDay(hour: 0, minute: 0);
          });
        },
        child: Text(label),
      ),
    );
  }

  String _formatDateTime(DateTime date, TimeOfDay time) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dayBeforeYesterday = today.subtract(const Duration(days: 2));
    final selectedDate = DateTime(date.year, date.month, date.day);

    String dateStr;
    if (selectedDate == today) {
      dateStr = '今天';
    } else if (selectedDate == yesterday) {
      dateStr = '昨天';
    } else if (selectedDate == dayBeforeYesterday) {
      dateStr = '前天';
    } else {
      dateStr = '${date.month}月${date.day}日';
    }

    final timeStr =
        '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    return '$dateStr $timeStr';
  }

  void _showDateTimePicker() async {
    // 先选择日期
    final selectedDate = await _showDatePicker();
    if (selectedDate != null) {
      setState(() {
        _selectedDate = selectedDate;
      });

      // 再选择时间
      if (mounted) {
        final selectedTime = await _showTimePicker();
        if (selectedTime != null) {
          setState(() {
            _selectedTime = selectedTime;
            _useQuickDate = false;
          });
        }
      }
    }
  }

  Future<DateTime?> _showDatePicker() async {
    return showFSheet<DateTime>(
      context: context,
      side: FLayout.btt,
      useSafeArea: true, // 确保使用安全区域
      builder: (context) => _buildDatePickerSheet(),
    );
  }

  Future<TimeOfDay?> _showTimePicker() async {
    return showFSheet<TimeOfDay>(
      context: context,
      side: FLayout.btt,
      useSafeArea: true, // 确保使用安全区域
      builder: (context) => _buildTimePickerSheet(),
    );
  }

  Widget _buildDatePickerSheet() {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      height: MediaQuery.of(context).size.height * 0.75, // 增加到75%给日历更多空间
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: colors.border, width: 0.5),
              ),
            ),
            child: Row(
              children: [
                FButton.icon(
                  style: FButtonStyle.ghost(),
                  onPress: () => Navigator.of(context).pop(),
                  child: Text(
                    '取消',
                    style: theme.typography.sm.copyWith(
                      color: colors.mutedForeground,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '选择日期',
                  style: theme.typography.base.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colors.foreground,
                  ),
                ),
                const Spacer(),
                FButton.icon(
                  style: FButtonStyle.ghost(),
                  onPress: () => Navigator.of(context).pop(_selectedDate),
                  child: Text(
                    '确定',
                    style: theme.typography.sm.copyWith(
                      color: colors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 日期选择器 - 使用滚动视图确保完整显示
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(16, 16, 16, 16), // 减少底部内边距
                child: FCalendar(
                controller: FCalendarController.date(
                  initialSelection: _selectedDate,
                  selectable: (date) {
                    // 只允许选择当前显示月份的日期
                    final now = DateTime.now();
                    final currentMonth = DateTime(now.year, now.month);
                    final dateMonth = DateTime(date.year, date.month);

                    // 允许选择当前月和前后几个月的日期
                    final monthDiff = (dateMonth.year - currentMonth.year) * 12 +
                                    (dateMonth.month - currentMonth.month);
                    return monthDiff.abs() <= 12; // 允许前后12个月
                  },
                ),
                start: DateTime(2020),
                end: DateTime(2030),
                today: DateTime.now(),
                onPress: (date) {
                  setState(() {
                    _selectedDate = date;
                  });
                },
                // 自定义日期显示，只显示纯数字，去除中文汉字"日"
                dayBuilder: (context, data, child) {
                  final theme = context.theme;
                  final colors = theme.colors;

                  // 判断日期状态
                  final isSelected = data.selected;
                  final isToday = data.today;
                  final isDisabled = !data.selectable;
                  final isCurrent = data.current; // 判断是否是当前月的日期

                  // 设置颜色和样式
                  Color backgroundColor = Colors.transparent;
                  Color textColor = colors.foreground;
                  FontWeight fontWeight = FontWeight.normal;

                  // 非当前月的日期显示为置灰且不可交互
                  if (!isCurrent) {
                    textColor = colors.mutedForeground.withOpacity(0.4);
                    fontWeight = FontWeight.w300;
                  } else if (isSelected) {
                    backgroundColor = colors.primary;
                    textColor = colors.primaryForeground;
                    fontWeight = FontWeight.w600;
                  } else if (isToday) {
                    backgroundColor = colors.primary.withOpacity(0.1);
                    textColor = colors.primary;
                    fontWeight = FontWeight.w500;
                  } else if (isDisabled) {
                    textColor = colors.mutedForeground;
                  }

                  return Container(
                    margin: const EdgeInsets.all(1), // 减少边距，给内容更多空间
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      borderRadius: BorderRadius.circular(6), // 稍微减小圆角
                      border: isToday && !isSelected && isCurrent
                        ? Border.all(color: colors.primary, width: 1)
                        : null,
                    ),
                    child: Center(
                      child: Text(
                        '${data.date.day}', // 只显示数字，不显示"日"
                        style: theme.typography.sm.copyWith(
                          color: textColor,
                          fontWeight: fontWeight,
                          fontSize: 15, // 稍微减小字体，确保在小屏幕上也能完整显示
                        ),
                      ),
                    ),
                  );
                },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimePickerSheet() {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      height: MediaQuery.of(context).size.height * 0.5, // 使用屏幕高度的50%
      decoration: BoxDecoration(
        color: colors.background,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(color: colors.border, width: 0.5),
              ),
            ),
            child: Row(
              children: [
                FButton.icon(
                  style: FButtonStyle.ghost(),
                  onPress: () => Navigator.of(context).pop(),
                  child: Text(
                    '取消',
                    style: theme.typography.sm.copyWith(
                      color: colors.mutedForeground,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '选择时间',
                  style: theme.typography.base.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colors.foreground,
                  ),
                ),
                const Spacer(),
                FButton.icon(
                  style: FButtonStyle.ghost(),
                  onPress: () => Navigator.of(context).pop(_selectedTime),
                  child: Text(
                    '确定',
                    style: theme.typography.sm.copyWith(
                      color: colors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 时间选择器
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 32), // 添加底部内边距
              child: Center(
                child: SizedBox(
                  width: 200,
                  child: FTimePicker(
                    controller: FTimePickerController(
                      initial: FTime(_selectedTime.hour, _selectedTime.minute),
                    ),
                    onChange: (time) {
                      setState(() {
                        _selectedTime = TimeOfDay(
                          hour: time.hour,
                          minute: time.minute,
                        );
                      });
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionField(BuildContext context) {
    final theme = context.theme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '描述',
          style: theme.typography.sm.copyWith(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        FTextField(
          controller: _descriptionController,
          hint: '记录这笔交易的详细信息...',
          maxLines: 2,
        ),
      ],
    );
  }

  Widget _buildTagsField(BuildContext context) {
    final suggestions = CommonTags.getTagsForType(_isExpense);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TagInputField(
          key: ValueKey(
            'tag_input_${_currentType}_${_tags.length}',
          ), // 使用type和长度作为key
          label: '标签',
          hint: '添加自定义标签',
          initialTags: _tags,
          onTagsChanged: (tags) {
            setState(() {
              _tags = tags;
            });
          },
          maxTags: 10, // 增加到10个标签
        ),
        const SizedBox(height: 12),
        TagSuggestions(
          suggestions: suggestions,
          selectedTags: _tags,
          onTagSelected: (tag) {
            if (!_tags.contains(tag) && _tags.length < 10) {
              setState(() {
                _tags.add(tag);
              });
            }
          },
        ),
      ],
    );
  }

  void _handleSubmit() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_keyboardController.isEmpty || _keyboardController.doubleValue <= 0) {
      _showError('请输入有效金额');
      return;
    }

    if (_selectedCategoryId == null) {
      _showError('请选择分类');
      return;
    }

    final amount = _keyboardController.doubleValue;

    // 构建最终的日期时间
    final finalDateTime = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );

    final formData = TransactionFormData(
      amount: amount,
      categoryId: _selectedCategoryId!,
      selectedDate: finalDateTime,
      description: _descriptionController.text.trim(),
      notes: _tags.join(','), // 将标签作为备注保存
      type: _currentType, // 添加类型参数
    );

    try {
      if (_isExpense) {
        await ref.read(manualEntryProvider.notifier).createExpense(formData);
      } else {
        await ref.read(manualEntryProvider.notifier).createIncome(formData);
      }

      final state = ref.read(manualEntryProvider);
      if (mounted) {
        if (state.successMessage != null) {
          _showSuccess(state.successMessage!);
          if (context.mounted) {
            context.pop();
          }
        } else if (state.error != null) {
          _showError(state.error!);
        }
      }
    } catch (e) {
      _showError('保存失败：${e.toString()}');
    }
  }

  Widget _buildAmountDisplay(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colors.background,
        border: Border(top: BorderSide(color: colors.border, width: 0.5)),
      ),
      child: Center(
        child: ListenableBuilder(
          listenable: _keyboardController,
          builder: (context, child) {
            final value = _keyboardController.value;
            final displayValue = value.isEmpty ? '0.00' : value;
            return Text(
              '¥$displayValue',
              style: theme.typography.xl2.copyWith(
                fontWeight: FontWeight.w700,
                color: colors.primary,
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildCompactAmountDisplay(BuildContext context) {
    final theme = context.theme;
    final colors = theme.colors;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colors.background,
        border: Border(top: BorderSide(color: colors.border, width: 0.5)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '金额',
            style: theme.typography.sm.copyWith(color: colors.mutedForeground),
          ),
          ListenableBuilder(
            listenable: _keyboardController,
            builder: (context, child) {
              final value = _keyboardController.value;
              final displayValue = value.isEmpty ? '0.00' : value;
              return Text(
                '¥$displayValue',
                style: theme.typography.lg.copyWith(
                  fontWeight: FontWeight.w700,
                  color: colors.primary,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _showError(String message) {
    if (_builderContext != null) {
      showFToast(
        context: _builderContext!,
        icon: const Icon(FIcons.triangleAlert),
        title: Text(message),
        alignment: FToastAlignment.topCenter,
        duration: const Duration(seconds: 3),
      );
    }
  }

  void _showSuccess(String message) {
    if (_builderContext != null) {
      showFToast(
        context: _builderContext!,
        icon: const Icon(FIcons.check),
        title: Text(message),
        alignment: FToastAlignment.topCenter,
        duration: const Duration(seconds: 2),
      );
    }
  }
}
